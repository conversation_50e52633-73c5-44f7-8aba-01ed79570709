package me.molhamsyr.infuseitems.listeners;

import me.molhamsyr.infuseitems.ItemManager;
import me.molhamsyr.infuseitems.abilities.Ability;
import me.molhamsyr.infuseitems.abilities.AbilityManager;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.List;

public class CloudPiercerListener implements Listener {

    List<Player> jumpers = new ArrayList<>();

    @EventHandler
    public void onDamage(EntityDamageEvent event) {

        if(event.getEntityType() != EntityType.PLAYER) return;

        Player player = (Player) event.getEntity();

        if(event.getCause() != EntityDamageEvent.DamageCause.FALL) return;

        if(AbilityManager.hasAbilityActivated(player, Ability.NO_FALL)) {
            event.setCancelled(true);
        }

        if(AbilityManager.hasAbilityActivated(player, Ability.CLOUD_LAUNCH)) {


            player.sendMessage("Test, DAMAGED ALL NEARBY PLAYERS");
            for(Entity entity : player.getNearbyEntities(8, 3, 8)) {

                if(!(entity instanceof Player)) continue;
                Player target = (Player) entity;

                if(target.getName().equals(player.getName())) continue;

                target.damage(12, player);


            }

        }


    }

    @EventHandler
    public void onClick(PlayerInteractEvent event) {

            Player player = event.getPlayer();

            if(event.getAction() != Action.RIGHT_CLICK_BLOCK && event.getAction() != Action.RIGHT_CLICK_AIR) return;
            if(!player.isSneaking()) return;

            ItemStack item = player.getInventory().getItemInMainHand();

            if(!ItemManager.isCloudPiercer(item)) return;

            if(AbilityManager.hasAbilityCooldown(player, Ability.CLOUD_LAUNCH)) {
                int seconds = AbilityManager.calculateCooldownSeconds(player, Ability.CLOUD_LAUNCH);
                player.sendMessage(ChatColor.RED + "You're still in cooldown! Wait for " + seconds + "s");
                return;
            }

            AbilityManager.activateAbility(player, Ability.CLOUD_LAUNCH);


            createParticleCloud(player);

            player.setVelocity(player.getVelocity().add(new Vector(0, 1.75, 0)));


    }

    public static void createParticleCloud(Player player) {
        // Get the player's location (at their feet)
        Location location = player.getLocation();

        // Create a small cloud of white particles
        for (int i = 0; i < 20; i++) {
            // Add some random offset to create a cloud effect
            double offsetX = (Math.random() - 0.5) * 0.5;
            double offsetY = Math.random() * 0.2;
            double offsetZ = (Math.random() - 0.5) * 0.5;

            // Spawn the particle
            player.getWorld().spawnParticle(
                    Particle.CLOUD, // Particle type
                    location.clone().add(offsetX, offsetY, offsetZ), // Location with offset
                    0, // Count (0 means 1 particle)
                    0, 0, 0, // Extra offsets (not needed here)
                    0 // Speed
            );
        }
    }

}
